import { ref, computed, watch } from 'vue'
import messages from '@/utils/messages'
import vehicleOrderApi from '@/api/vehicleOrder'
import { stocksApi } from '@/api/stocks'
import salesPriceLimitRuleApi from '@/api/salesPriceLimitRule'
import { useDictOptions } from '@/utils/dictUtils'
import { convertCentsToYuan } from '@/utils/money'

/**
 * 出库单弹窗组合式函数
 * @param {Object} props - 组件属性
 * @param {Function} emit - 组件事件发射器
 * @returns {Object} 返回响应式数据和方法
 */
export function useOutboundBillModal(props, emit) {
  // ==================== 响应式数据 ====================

  // 组件状态
  const formRef = ref(null)
  const remarkSectionRef = ref(null)
  const vehicleSelectorVisible = ref(false)
  const vinSelectorVisible = ref(false)
  const saving = ref(false) // 保存状态

  // 出库单位选择器相关
  const selectedOutboundOrg = ref(null)

  // 销售限价验证错误信息
  const salePriceLimitError = ref('')

  // VIN销售限价相关状态
  const currentVinPriceLimit = ref(null) // 当前VIN的销售限价（单位：分）
  const vinPriceLimitTimer = ref(null) // 防抖定时器

  // 贷款渠道选项 - 使用响应式字典数据
  const { options: loanChannelOptions } = useDictOptions('loan_channel', false)

  // 贷款期数选项 - 使用响应式字典数据
  const { options: rawLoanMonthsOptions } = useDictOptions('loan_months', false)

  // 处理贷款期数选项的数据类型转换
  const loanMonthsOptions = computed(() => {
    return rawLoanMonthsOptions.value.map(item => ({
      label: item.label,
      value: parseInt(item.value)
    }))
  })

  // 弹窗可见性
  const modelVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
  })

  // 表单数据
  const form = ref({
    // 基础信息
    id: null, // 出库单ID

    // 客户信息 - 使用API字段名
    customerId: null,
    customerName: '',
    mobile: '',
    customerAddress: '',
    customerEmail: '',
    salesAgentId: null, // 新增：销售顾问ID
    salesAgentName: '',
    salesOrgId: null, // 新增：销售单位ID
    salesOrgName: '',
    depositAmount: 0,
    depositDeductible: false,
    depositType: '', // 新增：定金类型

    // 产品信息 - 使用API字段名
    orderSn: '',
    orderStatus: '',
    orderDate: '', // 订单日期（对应销售日期）
    dealDate: '', // 成交日期（对应销售日期显示）
    deliveryDate: '',
    skuId: null,
    brand: '',
    series: '',
    configName: '',
    color: '',
    vin: '', // VIN码
    deliveryOrgId: null,
    deliveryOrgName: '',
    outboundOrgId: null, // 出库单位ID
    outboundOrgName: '', // 出库单位名称
    salesAmount: 0, // 启票价格
    invoiceAmount: 0, // 新增：发票金额
    sbAmount: 0, // 新增：SB金额
    dealAmount: 0, // 成交价格
    dealAmountCn: '', // 新增：成交金额大写
    discountAmount: 0,
    discountDeductible: false, // 新增：折扣是否转车款

    // 专享折扣相关 - 新增字段
    hasExclusiveDiscount: 'NO', // 是否有专享折扣
    exclusiveDiscountType: '', // 专享折扣类型
    exclusiveDiscountAmount: 0, // 专享折扣金额（保留兼容性）
    exclusiveDiscountPayableDeductible: false, // 专享折扣是否转车款（保留兼容性）
    exclusiveDiscountReceivableAmount: 0, // 应收-厂家-专项优惠金额
    exclusiveDiscountPayableAmount: 0, // 应付-客户-专项优惠金额
    exclusiveDiscountPayableDeductible: true, // 应付客户专项优惠是否转车款，默认为是
    exclusiveDiscountRemark: '', // 专享折扣备注

    // 付款方式 - 使用API字段名
    paymentMethod: '',
    loanChannel: null,
    loanAmount: 0,
    loanInitialAmount: 0,
    loanInitialRatio: 0,
    loanMonths: 12, // 分期期数，默认12期
    loanFee: 0, // 分期服务费
    loanRebateAmount: 0, // 新增：分期返利金额
    loanRebatePayableDeductible: false, // 新增：分期返利是否转车款
    loanRebateReceivableAmount: 0, // 应收机构分期返利
    loanRebatePayableAmount: 0, // 应付客户分期返利
    loanRebatePayableDeductible: false, // 新增：应付分期返利是否转车款

    // 车辆置换 - 使用API字段名
    usedVehicleAmount: 0,
    usedVehicleBrand: '',
    usedVehicleColor: '',
    usedVehicleDiscountAmount: 0,
    usedVehicleId: null,
    usedVehicleModel: '',
    usedVehicleVin: '',
    usedVehicleDeductibleAmount: 0,
    usedVehicleDiscountReceivableAmount: 0,
    usedVehicleDiscountPayableAmount: 0,
    usedVehicleDiscountPayableDeductible: true, // 新增：应付客户置换补贴是否转车款，默认为选中状态

    // 财务相关 - 使用API字段名
    grossProfitAmount: 0,
    grossProfitRate: 0,

    // 其他信息
    createTime: '',
    updateTime: '',
    creatorId: null,
    creatorName: '',
    editorId: null,
    editorName: '',
    salesLeaderId: null,
    salesLeaderName: '',

    // 车辆详细信息
    displacement: '',
    engineType: '',
    transmissionType: '',
    year: null,

    // 订单备注
    remark: '',
    paymentRemark: '', // RemarkSection组件使用的字段

    // 保留组件需要的字段
    hasUsedVehicle: 'NO',
    hasInsurance: 'NO',
    insuranceOrgId: null,
    insuranceOrgName: '',
    hasDerivativeIncome: 'NO',

    // 售前衍生收入相关字段 - 9个字段
    notaryFee: 0,                    // 公证费
    carefreeIncome: 0,               // 畅行无忧收入
    extendedWarrantyIncome: 0,       // 延保收入
    vpsIncome: 0,                    // VPS收入
    preInterest: 0,                  // 前置利息
    licensePlateFee: 0,              // 挂牌费
    tempPlateFee: 0,                 // 临牌费
    deliveryEquipment: 0,            // 外卖装具收入
    otherIncome: 0                   // 其他收入
  })

  // 表单验证规则 - VIN字段为必填
  const rules = {
    vin: [
      {
        required: true,
        message: '请选择VIN',
        trigger: ['blur', 'change']
      }
    ]
  }

  // ==================== 监听器 ====================

  // 监听orderId变化，加载订单数据
  watch(() => props.orderId, async (newOrderId) => {
    if (newOrderId && props.visible) {
      await loadOrderData(newOrderId)
    }
  }, { immediate: true })

  // 监听弹窗显示状态
  watch(() => props.visible, async (visible) => {
    if (visible) {
      // 重置保存状态
      saving.value = false
      if (props.orderId) {
        await loadOrderData(props.orderId)
      }
    }
  })

  // 监听VIN和销售机构变化，重新查询VIN销售限价
  watch([() => form.value.vin, () => form.value.salesOrgId], ([newVin, newSalesOrgId]) => {
    console.log('VIN或销售机构发生变化:', {
      newVin,
      newSalesOrgId,
      oldVin: form.value.vin,
      oldSalesOrgId: form.value.salesOrgId
    })

    // 清除之前的定时器
    if (vinPriceLimitTimer.value) {
      clearTimeout(vinPriceLimitTimer.value)
    }

    if (newVin && newSalesOrgId) {
      console.log('准备查询VIN限价（防抖）:', { newVin, newSalesOrgId })
      // 使用防抖机制，避免短时间内重复请求
      vinPriceLimitTimer.value = setTimeout(() => {
        console.log('执行VIN限价查询:', { newVin, newSalesOrgId })
        fetchVinPriceLimit(newVin, newSalesOrgId)
      }, 100) // 100ms防抖延迟
    } else {
      console.log('VIN或销售机构为空，清除限价:', { newVin, newSalesOrgId })
      currentVinPriceLimit.value = null
    }
  })

  // ==================== 方法 ====================

  // 加载订单数据
  const loadOrderData = async (orderId) => {
    try {
      const response = await vehicleOrderApi.getOrderDetail(orderId)
      if (response.code === 200) {
        setFormData(response.data)
      } else {
        messages.error(response.message || '获取订单详情失败')
      }
    } catch (error) {
      console.error('获取订单详情失败:', error)
      messages.error('获取订单详情失败，请稍后重试')
    }
  }

  // 更新弹窗可见性
  const updateVisible = (val) => {
    emit('update:visible', val)
  }



  // 查询VIN销售限价
  const fetchVinPriceLimit = async (vin, salesOrgId) => {
    if (!vin || !salesOrgId) {
      currentVinPriceLimit.value = null
      return
    }

    try {
      const response = await salesPriceLimitRuleApi.getPriceLimitByVin(vin, salesOrgId)
      if (response && response.data && response.data.priceLimit) {
        currentVinPriceLimit.value = response.data.priceLimit // 保存限价（单位：分）
        console.log('VIN限价查询成功:', { vin, salesOrgId, priceLimit: response.data.priceLimit })
      } else {
        currentVinPriceLimit.value = null
        console.log('VIN限价查询无结果:', { vin, salesOrgId })
      }
    } catch (error) {
      console.error('查询VIN销售限价失败:', error)
      currentVinPriceLimit.value = null
    }
  }

  // 显示车辆选择器
  const showVehicleSelector = () => {
    vehicleSelectorVisible.value = true
  }

  // 显示VIN选择器
  const showVinSelector = () => {
    vinSelectorVisible.value = true
  }

  // 处理VIN选择
  const handleVinSelected = async (selectedVehicle) => {
    console.log('handleVinSelected 被调用，选中的车辆数据:', selectedVehicle)

    if (selectedVehicle && selectedVehicle.vin) {
      form.value.vin = selectedVehicle.vin
      console.log('设置VIN:', selectedVehicle.vin)
      console.log('当前表单数据:', {
        vin: form.value.vin,
        salesOrgId: form.value.salesOrgId,
        salesOrgName: form.value.salesOrgName
      })

      // 清除之前的销售限价验证错误
      salePriceLimitError.value = ''

    } else {
      console.log('选中的车辆数据无效:', selectedVehicle)
    }
    vinSelectorVisible.value = false
  }

  // 处理VIN选择器取消
  const handleVinSelectorCancel = () => {
    vinSelectorVisible.value = false
  }



  // 处理车辆选择
  const handleVehicleSelected = (vehicle) => {
    form.value.skuId = vehicle.id
    form.value.brand = vehicle.brand
    form.value.series = vehicle.series
    form.value.configName = vehicle.configName
    form.value.modelYear = vehicle.modelYear
    form.value.color = vehicle.color
    form.value.vin = vehicle.vin
    form.value.salePrice = vehicle.salePrice || 0
    vehicleSelectorVisible.value = false

    // 重新计算最终价格
    calculateFinalPrice()
  }

  // 计算最终价格
  const calculateFinalPrice = () => {
    const salePrice = form.value.salePrice || 0
    const discount = form.value.discount || 0
    // 销售费用字段已移除
    form.value.finalPrice = salePrice - discount
  }

  // 处理销售价格变化
  const handleSalePriceChange = (value) => {
    form.value.salePrice = value
    calculateFinalPrice()
  }

  // 处理折扣变化
  const handleDiscountChange = (value) => {
    form.value.discount = value
    calculateFinalPrice()
  }



  // 处理专享折扣变化
  const handleExclusiveDiscountChange = (value) => {
    form.value.exclusiveDiscountAmount = value
  }

  // 处理应收厂家专项优惠金额变化
  const handleExclusiveDiscountReceivableAmountChange = (value) => {
    form.value.exclusiveDiscountReceivableAmount = value
  }

  // 处理应付客户专项优惠金额变化
  const handleExclusiveDiscountPayableAmountChange = (value) => {
    form.value.exclusiveDiscountPayableAmount = value
  }

  // 处理应付客户专项优惠转车款选项变化
  const handleExclusiveDiscountPayableDeductibleChange = (value) => {
    form.value.exclusiveDiscountPayableDeductible = value
  }

  // 处理贷款金额变化 - 出库单中不进行计算，只更新数值
  const handleLoanAmountChange = () => {
    // 出库单中不需要重新计算，分期金额在订单创建时已确定
    // 这里保留函数是为了保持组件接口一致性
  }

  // 处理首付金额变化 - 出库单中不进行计算，只更新数值
  const handleLoanInitialAmountChange = () => {
    // 出库单中不需要重新计算，首付金额在订单创建时已确定
    // 这里保留函数是为了保持组件接口一致性
  }

  // 处理贷款手续费变化
  const handleLoanFeeChange = (value) => {
    form.value.loanFee = value
  }

  // 处理转车款金额变化
  const handleUsedVehicleDeductibleAmountChange = (value) => {
    form.value.usedVehicleDeductibleAmount = value
  }

  // 处理应付客户置换补贴金额变化
  const handleUsedVehicleDiscountPayableAmountChange = (value) => {
    form.value.usedVehicleDiscountPayableAmount = value
  }

  // 处理应付客户置换补贴转车款选项变化
  const handleUsedVehicleDiscountPayableDeductibleChange = (value) => {
    form.value.usedVehicleDiscountPayableDeductible = value
  }

  // 处理衍生收入总金额变化
  const handleDerivativeIncomeTotalChange = () => {
    // 这里不需要做任何处理，因为衍生收入总金额是由各个字段计算得出的
    // 这个函数主要是为了保持组件接口的一致性
  }

  // 处理出库单位变化
  const handleOutboundOrgChange = (org) => {
    if (org) {
      form.value.outboundOrgId = org.id
      form.value.outboundOrgName = org.orgName || org.name
      selectedOutboundOrg.value = org
    } else {
      form.value.outboundOrgId = null
      form.value.outboundOrgName = ''
      selectedOutboundOrg.value = null
    }
  }

  // 验证车辆售价是否低于销售限价
  const validateSalePrice = () => {
    const salePrice = form.value.salesAmount || 0
    const limitPrice = form.value.dealAmount || 0

    if (limitPrice > 0 && salePrice < limitPrice) {
      salePriceLimitError.value = `车辆售价不能低于限价${limitPrice}元`
      return false
    } else {
      salePriceLimitError.value = ''
      return true
    }
  }

  // 处理保存
  const handleSave = async () => {
    try {
      await formRef.value?.validate()

      // 验证车辆售价是否低于销售限价
      if (!validateSalePrice()) {
        messages.error('车辆售价验证失败，请检查售价设置')
        return
      }

      // VIN销售限价检查
      if (currentVinPriceLimit.value && form.value.dealAmount) {
        const dealAmountInCents = Math.round(form.value.dealAmount * 100) // 将成交价从元转换为分
        if (dealAmountInCents < currentVinPriceLimit.value) {
          const priceLimitInYuan = (currentVinPriceLimit.value / 100).toFixed(2) // 将限价从分转换为元
          messages.error(`成交价不得低于销售限价${priceLimitInYuan}元`)
          return
        }
      }

      // 设置保存状态
      saving.value = true

      // 构建提交数据，仅包含指定字段
      const submitData = {
        orderId: props.orderId, // 订单ID
        outboundId: props.outboundId || null, // 出库单ID - 使用props传入的outboundId
        vin: form.value.vin, // VIN码

        // 是否有衍生收入
        hasDerivativeIncome: form.value.hasDerivativeIncome || 'NO',

        // 保险相关字段
        hasInsurance: form.value.hasInsurance || 'NO',
        insuranceOrgId: form.value.insuranceOrgId || null,
        insuranceOrgName: form.value.insuranceOrgName || '',

        // 应收-机构-分期返利（转换为分）
        loanRebateReceivableAmount: form.value.loanRebateReceivableAmount ? Math.round(form.value.loanRebateReceivableAmount * 100) : 0,

        // 应收-厂家-专项优惠（转换为分）
        exclusiveDiscountReceivableAmount: form.value.exclusiveDiscountReceivableAmount ? Math.round(form.value.exclusiveDiscountReceivableAmount * 100) : 0,

        // 应收-厂家-置换补贴（转换为分）
        usedVehicleDiscountReceivableAmount: form.value.usedVehicleDiscountReceivableAmount ? Math.round(form.value.usedVehicleDiscountReceivableAmount * 100) : 0,

        // 所有衍生收入字段（转换为分）
        notaryFee: form.value.notaryFee ? Math.round(form.value.notaryFee * 100) : 0,
        carefreeIncome: form.value.carefreeIncome ? Math.round(form.value.carefreeIncome * 100) : 0,
        extendedWarrantyIncome: form.value.extendedWarrantyIncome ? Math.round(form.value.extendedWarrantyIncome * 100) : 0,
        vpsIncome: form.value.vpsIncome ? Math.round(form.value.vpsIncome * 100) : 0,
        preInterest: form.value.preInterest ? Math.round(form.value.preInterest * 100) : 0,
        licensePlateFee: form.value.licensePlateFee ? Math.round(form.value.licensePlateFee * 100) : 0,
        tempPlateFee: form.value.tempPlateFee ? Math.round(form.value.tempPlateFee * 100) : 0,
        deliveryEquipment: form.value.deliveryEquipment ? Math.round(form.value.deliveryEquipment * 100) : 0,
        otherIncome: form.value.otherIncome ? Math.round(form.value.otherIncome * 100) : 0
      }

      // 获取合并后的备注数据
      if (remarkSectionRef.value?.getUpdatedRemarkData) {
        submitData.remark = remarkSectionRef.value.getUpdatedRemarkData()
        console.log('合并后的备注数据:', submitData.remark)
      } else {
        console.log('remarkSectionRef 不可用，使用原始备注:', form.value.remark)
        submitData.remark = form.value.remark || ''
      }

      console.log('提交出库更新数据:', submitData)
      console.log('保险相关字段提交数据:', {
        hasInsurance: submitData.hasInsurance,
        insuranceOrgId: submitData.insuranceOrgId,
        insuranceOrgName: submitData.insuranceOrgName
      })
      console.log('专项优惠相关字段提交数据:', {
        exclusiveDiscountReceivableAmount: submitData.exclusiveDiscountReceivableAmount,
        usedVehicleDiscountReceivableAmount: submitData.usedVehicleDiscountReceivableAmount,
        loanRebateReceivableAmount: submitData.loanRebateReceivableAmount
      })

      // 调用专用更新接口
      const response = await vehicleOrderApi.updateVehicleOutbound(submitData)

      if (response.code === 200) {
        messages.success('出库信息更新成功')
        emit('save')
        modelVisible.value = false
      } else {
        messages.error(response.message || '更新失败')
      }
    } catch (error) {
      console.error('保存失败:', error)
      messages.error('保存失败，请检查表单数据')
    } finally {
      // 无论成功还是失败都要重置保存状态
      saving.value = false
    }
  }

  // 处理取消
  const handleCancel = () => {
    emit('cancel')
    modelVisible.value = false
  }

  // 重置表单
  const resetForm = () => {
    // 重置保存状态
    saving.value = false

    form.value = {
      // 基础信息
      id: null, // 出库单ID

      // 客户信息 - 使用API字段名
      customerId: null,
      customerName: '',
      mobile: '',
      customerAddress: '',
      customerEmail: '',
      salesAgentId: null, // 新增：销售顾问ID
      salesAgentName: '',
      salesOrgId: null, // 新增：销售单位ID
      salesOrgName: '',
      depositAmount: 0,
      depositDeductible: false,
      depositType: '', // 新增：定金类型

      // 产品信息 - 使用API字段名
      orderSn: '',
      orderStatus: '',
      orderDate: '', // 订单日期（对应销售日期）
      dealDate: '', // 成交日期（对应销售日期显示）
      deliveryDate: '',
      skuId: null,
      brand: '',
      series: '',
      configName: '',
      color: '',
      vin: '', // VIN码
      deliveryOrgId: null,
      deliveryOrgName: '',
      outboundOrgId: null, // 出库单位ID
      outboundOrgName: '', // 出库单位名称
      salesAmount: 0, // 启票价格
      invoiceAmount: 0, // 新增：发票金额
      sbAmount: 0, // 新增：SB金额
      dealAmount: 0, // 成交价格
      dealAmountCn: '', // 新增：成交金额大写
      discountAmount: 0,
      discountDeductible: false, // 新增：折扣是否转车款

      // 专享折扣相关 - 新增字段
      hasExclusiveDiscount: 'NO', // 是否有专享折扣
      exclusiveDiscountType: '', // 专享折扣类型
      exclusiveDiscountAmount: 0, // 专享折扣金额（保留兼容性）
      exclusiveDiscountPayableDeductible: false, // 专享折扣是否转车款（保留兼容性）
      exclusiveDiscountReceivableAmount: 0, // 应收-厂家-专项优惠金额
      exclusiveDiscountPayableAmount: 0, // 应付-客户-专项优惠金额
      exclusiveDiscountPayableDeductible: true, // 应付客户专项优惠是否转车款，默认为是
      exclusiveDiscountRemark: '', // 专享折扣备注

      // 付款方式 - 使用API字段名
      paymentMethod: '',
      loanChannel: null,
      loanAmount: 0,
      loanInitialAmount: 0,
      loanInitialRatio: 0,
      loanMonths: 12, // 分期期数，默认12期
      loanFee: 0, // 分期服务费
      loanRebateAmount: 0, // 新增：分期返利金额
      loanRebatePayableDeductible: false, // 新增：分期返利是否转车款
      loanRebateReceivableAmount: 0, // 应收机构分期返利
      loanRebatePayableAmount: 0, // 应付客户分期返利
      loanRebatePayableDeductible: false, // 新增：应付分期返利是否转车款

      // 车辆置换 - 使用API字段名
      usedVehicleAmount: 0,
      usedVehicleBrand: '',
      usedVehicleColor: '',
      usedVehicleDiscountAmount: 0,
      usedVehicleId: null,
      usedVehicleModel: '',
      usedVehicleVin: '',
      usedVehicleDeductibleAmount: 0,
      usedVehicleDiscountReceivableAmount: 0,
      usedVehicleDiscountPayableAmount: 0,
      usedVehicleDiscountPayableDeductible: true, // 新增：应付客户置换补贴是否转车款，默认为选中状态

      // 财务相关 - 使用API字段名
      grossProfitAmount: 0,
      grossProfitRate: 0,

      // 其他信息
      createTime: '',
      updateTime: '',
      creatorId: null,
      creatorName: '',
      editorId: null,
      editorName: '',
      salesLeaderId: null,
      salesLeaderName: '',

      // 车辆详细信息
      displacement: '',
      engineType: '',
      transmissionType: '',
      year: null,

      // 订单备注
      remark: '',
      paymentRemark: '', // RemarkSection组件使用的字段

      // 保留组件需要的字段
      hasUsedVehicle: 'NO',
      hasInsurance: 'NO',
      insuranceOrgId: null,
      insuranceOrgName: '',
      hasDerivativeIncome: 'NO',

      // 售前衍生收入相关字段 - 9个字段
      notaryFee: 0,                    // 公证费
      carefreeIncome: 0,               // 畅行无忧收入
      extendedWarrantyIncome: 0,       // 延保收入
      vpsIncome: 0,                    // VPS收入
      preInterest: 0,                  // 前置利息
      licensePlateFee: 0,              // 挂牌费
      tempPlateFee: 0,                 // 临牌费
      deliveryEquipment: 0,            // 外卖装具收入
      otherIncome: 0                   // 其他收入
    }
    selectedOutboundOrg.value = null
  }

  // 设置表单数据
  const setFormData = (data) => {
    // 处理日期字段 - 将时间戳转换为日期字符串
    const formatDate = (timestamp) => {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleDateString('zh-CN')
    }



    // 直接使用API返回的字段名称，与模板保持一致
    const mappedData = {
      // 基础信息
      id: data.id || data.outboundBillId || null, // 出库单ID

      // 客户信息 - 需要从其他地方获取，新API结构中没有这些字段
      customerId: data.customerId,
      customerName: data.customerName || '', // 新API中可能没有，需要从其他接口获取
      mobile: data.mobile || '',
      customerAddress: data.customerAddress || '',
      customerEmail: data.customerEmail || '',
      salesAgentId: data.salesAgentId || null, // 销售顾问ID
      salesAgentName: data.salesAgentName || '', // 需要根据salesAgentId获取
      salesOrgId: data.salesOrgId || null, // ✅ 添加销售机构ID映射
      salesOrgName: data.salesOrgName || '', // 需要根据salesOrgId获取
      depositAmount: convertCentsToYuan(data.depositAmount || 0),
      depositDeductible: Boolean(data.depositDeductible),

      // 产品信息 - 使用新API字段名
      orderSn: data.orderSn || '',
      orderStatus: data.orderStatus || '',
      orderDate: data.orderDate, // 保存原始订单日期时间戳
      dealDate: formatDate(data.orderDate), // 销售日期使用orderDate字段进行格式化显示
      deliveryDate: formatDate(data.deliveryDate),
      skuId: data.skuId,
      brand: data.brand || '', // 需要根据skuId获取车辆信息
      series: data.series || '',
      configName: data.configName || '',
      color: data.colorCode || data.color || '',
      vin: data.vin || '', // VIN码
      deliveryOrgId: data.deliveryOrgId,
      deliveryOrgName: data.deliveryOrgName || '', // 需要根据deliveryOrgId获取
      outboundOrgId: data.outboundOrgId || data.deliveryOrgId, // 出库单位ID，优先使用outboundOrgId，否则使用deliveryOrgId
      outboundOrgName: data.outboundOrgName || data.deliveryOrgName || '', // 出库单位名称
      salesAmount: convertCentsToYuan(data.salesAmount || 0), // 启票价格
      dealAmount: convertCentsToYuan(data.dealAmount || 0), // 成交价格
      discountAmount: convertCentsToYuan(data.discountAmount || 0),
      discountDeductible: Boolean(data.discountDeductible), // 现金优惠是否转车款

      // 付款方式 - 使用新API字段名
      paymentMethod: data.paymentMethod || '',
      loanChannel: data.loanChannel || '',
      loanAmount: convertCentsToYuan(data.loanAmount || 0),
      loanInitialAmount: convertCentsToYuan(data.loanInitialAmount || 0),
      loanInitialRatio: data.loanInitialRatio || 0, // 新API中已经是百分比
      loanMonths: data.loanMonths || 12, // 分期期数，默认12期
      loanFee: convertCentsToYuan(data.loanFee || 0), // 分期服务费
      loanRebateAmount: convertCentsToYuan(data.loanRebateAmount || 0), // 分期返利金额
      loanRebatePayableDeductible: Boolean(data.loanRebatePayableDeductible), // 分期返利是否转车款
      loanRebateReceivableAmount: convertCentsToYuan(data.loanRebateReceivableAmount || 0), // 应收机构分期返利
      loanRebatePayableAmount: convertCentsToYuan(data.loanRebatePayableAmount || 0), // 应付客户分期返利
      loanRebatePayableDeductible: Boolean(data.loanRebatePayableDeductible), // 应付分期返利是否转车款

      // 车辆置换 - 使用新API字段名
      usedVehicleAmount: convertCentsToYuan(data.usedVehicleAmount || 0),
      usedVehicleBrand: data.usedVehicleBrand || '',
      usedVehicleColor: data.usedVehicleColor || '',
      usedVehicleDiscountAmount: convertCentsToYuan(data.usedVehicleDiscountAmount || 0),
      usedVehicleId: data.usedVehicleId || '',
      usedVehicleModel: data.usedVehicleModel || '',
      usedVehicleVin: data.usedVehicleVin || '',
      usedVehicleDeductibleAmount: convertCentsToYuan(data.usedVehicleDeductibleAmount || 0),
      usedVehicleDiscountReceivableAmount: convertCentsToYuan(data.usedVehicleDiscountReceivableAmount || 0),
      usedVehicleDiscountPayableAmount: convertCentsToYuan(data.usedVehicleDiscountPayableAmount || 0),
      usedVehicleDiscountPayableDeductible: Boolean(data.usedVehicleDiscountPayableDeductible), // 置换补贴是否转车款（保留兼容性）
      usedVehicleDiscountPayableDeductible: Boolean(data.usedVehicleDiscountPayableDeductible !== undefined ? data.usedVehicleDiscountPayableDeductible : true), // 应付客户置换补贴是否转车款，默认为true

      // 财务相关 - 使用新API字段名
      grossProfitAmount: convertCentsToYuan(data.grossProfitAmount || 0),
      grossProfitRate: data.grossProfitRate || 0,
      sbAmount: convertCentsToYuan(data.sbAmount || 0), // 新增：SB金额
      invoiceAmount: convertCentsToYuan(data.invoiceAmount || 0), // 新增：发票金额
      dealAmountCn: data.dealAmountCn || '', // 新增：成交金额大写

      // 专享折扣相关 - 直接使用接口返回的hasExclusiveDiscount字段
      hasExclusiveDiscount: data.hasExclusiveDiscount || 'NO',
      exclusiveDiscountType: data.exclusiveDiscountType || '',
      exclusiveDiscountAmount: convertCentsToYuan(data.exclusiveDiscountAmount || 0),
      exclusiveDiscountPayableDeductible: Boolean(data.exclusiveDiscountPayableDeductible),
      exclusiveDiscountReceivableAmount: convertCentsToYuan(data.exclusiveDiscountReceivableAmount || 0),
      exclusiveDiscountPayableAmount: convertCentsToYuan(data.exclusiveDiscountPayableAmount || 0),
      exclusiveDiscountPayableDeductible: Boolean(data.exclusiveDiscountPayableDeductible),
      exclusiveDiscountRemark: data.exclusiveDiscountRemark || '',

      // 其他信息
      createTime: data.createTime || '',
      updateTime: data.updateTime || '',
      creatorId: data.creatorId,
      creatorName: data.creatorName || '',
      editorId: data.editorId,
      editorName: data.editorName || '',
      salesLeaderId: data.salesLeaderId,
      salesLeaderName: data.salesLeaderName || '',
      salesAgentId: data.salesAgentId, // 新增：销售顾问ID

      // 车辆详细信息
      displacement: data.displacement || '',
      engineType: data.engineType || '',
      transmissionType: data.transmissionType || '',
      year: data.year,

      // 订单备注
      remark: data.remark || '',
      paymentRemark: data.remark || '', // RemarkSection组件使用的字段，与remark保持同步

      // 保险信息 - 使用新API字段
      hasInsurance: data.hasInsurance || 'NO',
      insuranceOrgId: data.insuranceOrgId,
      insuranceOrgName: data.insuranceOrgName || '',

      // 车辆置换状态判断
      hasUsedVehicle: (data.usedVehicleVin && data.usedVehicleVin.trim() !== '') ? 'YES' : 'NO',

      // 售前衍生收入相关字段 - 9个字段，从API数据中提取并转换
      notaryFee: convertCentsToYuan(data.notaryFee || 0),                    // 公证费
      carefreeIncome: convertCentsToYuan(data.carefreeIncome || 0),          // 畅行无忧收入
      extendedWarrantyIncome: convertCentsToYuan(data.extendedWarrantyIncome || 0), // 延保收入
      vpsIncome: convertCentsToYuan(data.vpsIncome || 0),                    // VPS收入
      preInterest: convertCentsToYuan(data.preInterest || 0),                // 前置利息
      licensePlateFee: convertCentsToYuan(data.licensePlateFee || 0),        // 挂牌费
      tempPlateFee: convertCentsToYuan(data.tempPlateFee || 0),              // 临牌费
      deliveryEquipment: convertCentsToYuan(data.deliveryEquipment || 0),    // 外卖装具收入
      otherIncome: convertCentsToYuan(data.otherIncome || 0),                // 其他收入

      // 根据是否有衍生收入数据来设置hasDerivativeIncome状态
      hasDerivativeIncome: data.hasDerivativeIncome || (
        (data.notaryFee && data.notaryFee > 0) ||
        (data.carefreeIncome && data.carefreeIncome > 0) ||
        (data.extendedWarrantyIncome && data.extendedWarrantyIncome > 0) ||
        (data.vpsIncome && data.vpsIncome > 0) ||
        (data.preInterest && data.preInterest > 0) ||
        (data.licensePlateFee && data.licensePlateFee > 0) ||
        (data.tempPlateFee && data.tempPlateFee > 0) ||
        (data.deliveryEquipment && data.deliveryEquipment > 0) ||
        (data.otherIncome && data.otherIncome > 0)
      ) ? 'YES' : 'NO'
    }

    // 更新表单数据
    Object.assign(form.value, mappedData)

    // 设置出库单位信息
    const outboundOrgId = data.outboundOrgId || data.deliveryOrgId
    const outboundOrgName = data.outboundOrgName || data.deliveryOrgName
    if (outboundOrgId) {
      selectedOutboundOrg.value = {
        id: outboundOrgId,
        orgName: outboundOrgName,
        name: outboundOrgName
      }
    }
  }

  // ==================== 监听器 ====================

  // 出库单中不需要监听车辆售价变化来重新计算分期金额
  // 分期相关数据在订单创建时已经确定，出库单只负责显示

  // 付款方式区域配置 - 只允许编辑应收-机构-分期返利字段
  const paymentMethodSectionConfig = {
    visible: true,
    editable: false, // 区域级别设为不可编辑
    fields: {
      // 付款方式选择 - 禁用
      paymentMethod: { visible: true, editable: false },

      // 分期相关字段 - 全部禁用
      loanChannel: { visible: true, editable: false },
      loanAmount: { visible: true, editable: false },
      loanInitialAmount: { visible: true, editable: false },
      loanInitialRatio: { visible: true, editable: false }, // 首付比例（计算字段，始终禁用）
      loanMonths: { visible: true, editable: false },
      loanFee: { visible: true, editable: false },

      // 应收-机构-分期返利 - 唯一允许编辑的字段
      loanRebateReceivableAmount: { visible: true, editable: true },

      // 应付-客户-分期返利 - 禁用
      loanRebatePayableAmount: { visible: true, editable: false },
      loanRebatePayableDeductible: { visible: true, editable: false }
    }
  }

  // 车辆置换区域配置 - 只允许编辑应收-厂家-置换补贴字段
  const vehicleExchangeSectionConfig = {
    visible: true,
    editable: false, // 区域级别设为不可编辑
    fields: {
      // 是否有车辆置换 - 禁用
      hasUsedVehicle: { visible: true, editable: false },

      // 置换车辆信息 - 全部禁用
      usedVehicleId: { visible: true, editable: false },
      usedVehicleVin: { visible: true, editable: false },
      usedVehicleBrand: { visible: true, editable: false },
      usedVehicleModel: { visible: true, editable: false },
      usedVehicleAmount: { visible: true, editable: false },
      usedVehicleDeductibleAmount: { visible: true, editable: false },

      // 应收-厂家-置换补贴 - 唯一允许编辑的字段
      usedVehicleDiscountReceivableAmount: { visible: true, editable: true },

      // 应付-客户-置换补贴 - 禁用
      usedVehicleDiscountPayableAmount: { visible: true, editable: false },
      usedVehicleDiscountPayableDeductible: { visible: true, editable: false }
    }
  }

  // 专项优惠区域配置 - 只允许编辑应收-厂家-专项优惠字段
  const exclusiveDiscountSectionConfig = {
    visible: true,
    editable: true, // 区域级别设为可编辑，通过字段级别控制
    fields: {
      // 是否有专项优惠 - 禁用
      hasExclusiveDiscount: { visible: true, editable: false },

      // 专项优惠类型 - 禁用
      exclusiveDiscountType: { visible: true, editable: false },

      // 应收-厂家-专项优惠 - 启用（这是唯一可编辑的字段）
      exclusiveDiscountReceivableAmount: { visible: true, editable: true },

      // 应付-客户-专项优惠 - 禁用
      exclusiveDiscountPayableAmount: { visible: true, editable: false },
      exclusiveDiscountPayableDeductible: { visible: true, editable: false },

      // 专项优惠备注 - 禁用
      exclusiveDiscountRemark: { visible: true, editable: false }
    }
  }

  // 返回对象
  return {
    // 响应式数据
    formRef,
    remarkSectionRef,
    vehicleSelectorVisible,
    vinSelectorVisible,
    selectedOutboundOrg,
    form,
    rules,
    loanChannelOptions,
    loanMonthsOptions,
    modelVisible,
    saving,
    salePriceLimitError,
    currentVinPriceLimit,

    // 区域配置
    paymentMethodSectionConfig,
    vehicleExchangeSectionConfig,
    exclusiveDiscountSectionConfig,

    // 基础方法
    updateVisible,
    showVehicleSelector,
    showVinSelector,
    fetchVinPriceLimit,

    // 事件处理函数
    handleVehicleSelected,
    handleVinSelected,
    handleVinSelectorCancel,
    handleSalePriceChange,
    handleDiscountChange,
    handleExclusiveDiscountChange,
    handleExclusiveDiscountReceivableAmountChange,
    handleExclusiveDiscountPayableAmountChange,
    handleExclusiveDiscountPayableDeductibleChange,
    handleLoanAmountChange,
    handleLoanInitialAmountChange,
    handleLoanFeeChange,
    handleUsedVehicleDeductibleAmountChange,
    handleUsedVehicleDiscountPayableAmountChange,
    handleUsedVehicleDiscountPayableDeductibleChange,
    handleDerivativeIncomeTotalChange,
    handleOutboundOrgChange,

    // 保存和重置函数
    handleSave,
    handleCancel,
    resetForm,
    setFormData
  }
}
