import { h } from 'vue'
import { NIcon, NTag, NSwitch, NSpin } from 'naive-ui'

/**
 * 创建销售限价规则列表表格列配置
 * @param {Object} icons - 图标对象
 * @param {Object} handlers - 事件处理函数对象
 * @returns {Array} 表格列配置数组
 */
export function createSalesPriceLimitTableColumns(icons, handlers) {
  const {
    SearchIconComponent,
    CreateOutlineIcon
  } = icons

  const {
    handleView,
    handleEdit,
    handleStatusChange,
    statusLoadingMap
  } = handlers

  return [
    { type: 'selection', width: 50, fixed: 'left' },

    {
      title: '规则名称',
      key: 'ruleName',
      width: 150,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      }
    },

    {
      title: '规则类型',
      key: 'ruleType',
      width: 100,
      render(row) {
        const typeMap = {
          'sku': { text: 'SKU', type: 'info' },
          'vin': { text: 'VIN', type: 'warning' }
        }

        const type = typeMap[row.ruleType] || { text: '未知', type: 'default' }

        return h(
          NTag,
          {
            type: type.type,
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => type.text }
        )
      }
    },


    {
      title: '限价金额(元)',
      key: 'priceLimit',
      width: 140,
      align: 'right',
      render(row) {
        if (row.priceLimit === null || row.priceLimit === undefined) {
          return h('span', { style: { color: '#909399' } }, '未设置')
        }
        // priceLimit单位是分，需要转换为元
        const amountInYuan = row.priceLimit / 100
        const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
        return h('span', { style: { fontWeight: 'bold', color: '#18a058' } }, `¥${formattedAmount}`)
      }
    },

    {
      title: '生效范围',
      align: 'center',
      key: 'effectiveOrgNames',
      width: 200,
      ellipsis: {
        tooltip: true
      },
      render(row) {
        if (!row.effectiveOrgNames) {
          return h('span', { style: {} }, '所有销售单位')
        }

        const orgNames = row.effectiveOrgNames.split(',')
        if (orgNames.length <= 2) {
          return h('span', {}, row.effectiveOrgNames)
        } else {
          return h('span', {}, `${orgNames[0]}等${orgNames.length}个`)
        }
      }
    },

    {
      title: '规则详情',
      key: 'ruleDetails',
      align: 'center',
      width: 120,
      render(row) {
        if (!row.ruleDetails) {
          return h('span', { style: { color: '#909399' } }, '无详情')
        }

        try {
          const details = JSON.parse(row.ruleDetails)
          const count = Array.isArray(details) ? details.length : 0
          const text = row.ruleType === 'vin' ? `共${count}个车架号` : `共${count}个在售车型`

          return h(
            NTag,
            {
              type: 'info',
              bordered: false,
              style: {
                padding: '2px 8px'
              }
            },
            { default: () => text }
          )
        } catch (error) {
          return h('span', { style: { color: '#909399' } }, '解析失败')
        }
      }
    },
    {
      title: '生效时间',
      key: 'effectiveTime',
      align: 'center',
      width: 180,
      render(row) {
        if (!row.effectiveTime) return '未设置'
        const date = new Date(row.effectiveTime)
        return date.toLocaleString('zh-CN')
      }
    },

    {
      title: '失效时间',
      key: 'expiryTime',
      align: 'center',
      width: 180,
      render(row) {
        if (!row.expiryTime) return '永久有效'
        const date = new Date(row.expiryTime)
        return date.toLocaleString('zh-CN')
      }
    },

    {
      title: '创建人',
      key: 'creatorName',
      width: 100
    },


    {
      title: '规则状态',
      key: 'status',
      width: 120,
      align: 'center',
      render(row) {
        const isLoading = statusLoadingMap && statusLoadingMap.value && statusLoadingMap.value[row.id]

        return h(
          'div',
          {
            style: {
              position: 'relative',
              display: 'inline-block'
            }
          },
          [
            // 开关组件
            h(
              NSwitch,
              {
                value: row.status === 'active',
                checkedValue: true,
                uncheckedValue: false,
                size: 'medium',
                disabled: isLoading, // loading时禁用开关
                'onUpdate:value': (value) => {
                  const newStatus = value ? 'active' : 'inactive'
                  handleStatusChange && handleStatusChange(row.id, newStatus)
                }
              },
              {
                checked: () => '启用',
                unchecked: () => '停用'
              }
            ),
            // loading覆盖层
            isLoading ? h(
              'div',
              {
                style: {
                  position: 'absolute',
                  top: '0',
                  left: '0',
                  right: '0',
                  bottom: '0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  borderRadius: '4px',
                  zIndex: 1
                }
              },
              [
                h(NSpin, { size: 'small' })
              ]
            ) : null
          ]
        )
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      align: 'center',
      fixed: 'right',
      render: (row) => {
        const actions = []

        // 查看按钮 - 所有状态都可以查看
        actions.push(
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#18a058',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '8px'
              },
              onClick: () => handleView && handleView(row.id),
              title: '查看详情'
            },
            [h(SearchIconComponent, { size: 20, color: '#18a058' })]
          )
        )

        // 编辑按钮 - 只有停用状态的规则才显示编辑图标
        if (row.status === 'inactive') {
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#18a058',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '8px'
                },
                onClick: () => handleEdit && handleEdit(row.id),
                title: '编辑规则'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CreateOutlineIcon) })]
            )
          )
        }

        return h(
          'div',
          {
            style: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }
          },
          actions
        )
      }
    }
  ]
}
