import { ref, reactive, computed, markRaw, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import {
    SearchOutline,
    RefreshOutline,
    AddOutline,
    CreateOutline
} from '@vicons/ionicons5'
import SearchIcon from '@/components/icons/SearchIcon.vue'

import { dateRangeOptions, getDateRangeParams } from '@/utils/dateRange'
import { createSalesPriceLimitTableColumns } from './SalesPriceLimitColumns.js'
import salesPriceLimitRuleApi from '@/api/salesPriceLimitRule.js'

/**
 * 销售限价规则页面逻辑
 */
export function useSalesPriceLimitPage() {
    // 消息
    const message = useMessage()

    // 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
    const SearchOutlineIcon = markRaw(SearchOutline)
    const RefreshOutlineIcon = markRaw(RefreshOutline)
    const AddOutlineIcon = markRaw(AddOutline)
    const SearchIconComponent = markRaw(SearchIcon)
    const CreateOutlineIcon = markRaw(CreateOutline)

    // 状态变量
    const tableRef = ref(null)
    const loading = ref(false)
    const selectedRows = ref([])

    // 数据存储
    const salesPriceLimitData = ref([])

    // 状态切换loading状态管理
    const statusLoadingMap = ref({})

    /**
     * 规则类型选项
     */
    const ruleTypeOptions = computed(() => [
        { label: '不限', value: null },
        { label: 'SKU', value: 'sku' },
        { label: 'VIN', value: 'vin' }
    ])

    /**
     * 规则状态选项
     */
    const ruleStatusOptions = computed(() => [
        { label: '不限', value: null },
        { label: '启用', value: 'active' },
        { label: '停用', value: 'inactive' }
    ])

    // 筛选表单
    const filterForm = reactive({
        dateRange: null,
        customDateRange: null,
        vehicleBrand: null,
        ruleType: null,
        ruleStatus: null,
        keywords: ''
    })

    // 分页配置 - 适配 n-data-table 内置分页
    const pagination = reactive({
        page: 1,
        pageSize: 20,
        itemCount: 0,
        showSizePicker: true,
        showQuickJumper: false,
        pageSizes: [20, 50, 100],
        prefix: ({ itemCount }) => `共 ${itemCount} 条`,
        displayOrder: ['prefix', 'pages', 'size-picker']
    })



    // 计算过滤后的数据（用于前端筛选和搜索）
    const filteredData = computed(() => {
        return salesPriceLimitData.value
    })

    /**
     * 窗口高度计算
     */
    const windowHeight = ref(window.innerHeight)
    const tableMaxHeight = computed(() => {
        // 计算表格最大高度：窗口高度 - 页面padding(32px) - 筛选卡片(约120px) - 工具栏(约60px) - 间距(32px)
        // 表格内置分页不需要额外预留空间
        return windowHeight.value - 244
    })

    // 监听窗口大小变化
    const handleResize = () => {
        windowHeight.value = window.innerHeight
    }

    onMounted(() => {
        window.addEventListener('resize', handleResize)
        fetchData()
    })

    onUnmounted(() => {
        window.removeEventListener('resize', handleResize)
    })

    /**
     * 处理日期范围变化
     */
    const handleDateRangeChange = (value) => {
        filterForm.dateRange = value
        if (value !== 'custom') {
            filterForm.customDateRange = null
        }
        pagination.page = 1
        fetchData()
    }

    /**
     * 处理自定义日期范围变化
     */
    const handleCustomDateChange = (value) => {
        filterForm.customDateRange = value
        pagination.page = 1
        fetchData()
    }

    /**
     * 处理搜索
     */
    const handleSearch = () => {
        pagination.page = 1
        fetchData()
    }

    /**
     * 获取列表数据
     */
    const fetchData = async () => {
        loading.value = true
        try {
            // 构建查询参数
            const params = {
                page: pagination.page,
                size: pagination.pageSize
            }

            // 添加筛选条件
            if (filterForm.keywords) {
                params.ruleName = filterForm.keywords
            }
            if (filterForm.ruleType) {
                params.ruleType = filterForm.ruleType
            }
            if (filterForm.ruleStatus) {
                params.status = filterForm.ruleStatus
            }

            // 添加日期范围筛选
            if (filterForm.dateRange) {
                const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
                if (dateRange.startDate) {
                    params.startDate = dateRange.startDate
                }
                if (dateRange.endDate) {
                    params.endDate = dateRange.endDate
                }
            }

            console.log('请求参数:', params)

            // 调用API获取数据
            const response = await salesPriceLimitRuleApi.getRuleList(params)

            console.log('API响应:', response)

            // 更新数据和分页信息
            if (response && response.data) {
                salesPriceLimitData.value = response.data.list || []
                pagination.itemCount = response.data.total || 0
            } else {
                salesPriceLimitData.value = []
                pagination.itemCount = 0
            }

        } catch (error) {
            console.error('获取数据失败:', error)
            message.error('获取数据失败')
            salesPriceLimitData.value = []
            pagination.itemCount = 0
        } finally {
            loading.value = false
        }
    }

    /**
     * 刷新数据列表
     */
    const refreshData = () => {
        fetchData()
    }



    /**
     * 处理选择变化
     */
    const handleSelectionChange = (keys) => {
        selectedRows.value = keys
    }

    /**
     * 处理页码变化
     */
    const handlePageChange = (page) => {
        pagination.page = page
        fetchData()
    }

    /**
     * 处理页面大小变化
     */
    const handlePageSizeChange = (pageSize) => {
        pagination.pageSize = pageSize
        pagination.page = 1
        fetchData()
    }

    /**
     * 复制到剪贴板
     */
    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text).then(() => {
            message.success('已复制到剪贴板')
        }).catch(() => {
            message.error('复制失败')
        })
    }

    /**
     * 查看详情
     */
    const handleView = (id) => {
        // 这个方法需要在组件中通过事件或其他方式调用
        // 暂时保留消息提示，实际调用会在组件中处理
        console.log('查看规则详情:', id)
    }

    /**
     * 编辑规则
     */
    const handleEdit = (id) => {
        // 这个方法需要在组件中通过事件或其他方式调用
        // 暂时保留消息提示，实际调用会在组件中处理
        console.log('编辑规则:', id)
    }

    /**
     * 状态变更
     */
    const handleStatusChange = async (id, newStatus) => {
        const statusText = {
            'active': '启用',
            'inactive': '停用'
        }

        try {
            // 设置loading状态
            statusLoadingMap.value[id] = true

            // 调用独立的状态控制接口，处理缓存相关的业务状态
            await salesPriceLimitRuleApi.updateRuleStatus(id, newStatus)

            message.success(`${statusText[newStatus]}规则成功`)

            // 刷新列表数据
            fetchData()

        } catch (error) {
            console.error('状态变更失败:', error)
            message.error(`${statusText[newStatus]}规则失败`)
        } finally {
            // 清除loading状态
            delete statusLoadingMap.value[id]
        }
    }

    // 表格列配置
    const columns = computed(() => {
        const icons = {
            SearchIconComponent,
            CreateOutlineIcon
        }

        const handlers = {
            handleView: (id) => {
                // 触发自定义事件，让父组件处理
                window.dispatchEvent(new CustomEvent('showRuleDetail', { detail: { id, mode: 'view' } }))
            },
            handleEdit: (id) => {
                // 触发自定义事件，让父组件处理
                window.dispatchEvent(new CustomEvent('showRuleDetail', { detail: { id, mode: 'edit' } }))
            },
            handleStatusChange,
            statusLoadingMap
        }

        return createSalesPriceLimitTableColumns(icons, handlers)
    })

    // 表格横向滚动宽度 - 根据实际列宽度计算
    const scrollX = computed(() => {
        // 计算所有列的总宽度
        // 选择列: 50px + 规则名称: 200px + 规则类型: 100px + 限价金额: 140px + 生效单位: 200px +
        // 规则详情: 120px + 规则状态: 100px + 创建人: 100px + 创建时间: 160px +
        // 生效时间: 160px + 失效时间: 160px + 操作: 200px
        // 总计: 50 + 200 + 100 + 140 + 200 + 120 + 100 + 100 + 160 + 160 + 160 + 200 = 1690
        return 1080
    })

    return {
        // 图标
        SearchOutlineIcon,
        RefreshOutlineIcon,
        AddOutlineIcon,
        SearchIconComponent,
        CreateOutlineIcon,

        // 状态
        tableRef,
        loading,
        selectedRows,
        statusLoadingMap,

        // 数据
        dateRangeOptions,
        ruleTypeOptions,
        ruleStatusOptions,
        filterForm,
        pagination,
        filteredData,
        columns,
        scrollX,
        tableMaxHeight,

        // 方法
        refreshData,
        handleDateRangeChange,
        handleCustomDateChange,
        handleSearch,
        handleSelectionChange,
        handlePageChange,
        handlePageSizeChange,
        handleView,
        handleEdit,
        handleStatusChange,
    }
}