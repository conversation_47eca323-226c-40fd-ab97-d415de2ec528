<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    :title="modalTitle"
    style="width: 1200px"
    :mask-closable="false"
    transform-origin="center"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="top"
      require-mark-placement="right-hanging"
    >
      <!-- 基本信息 -->
      <n-card>
        <!-- 第一行：规则名称 + 销售限价 -->
        <div class="form-row">
          <n-form-item label="规则名称" path="ruleName" class="form-item">
            <n-input
              v-model:value="formData.ruleName"
              placeholder="请输入规则名称"
              maxlength="50"
              show-count
              :readonly="isReadonly"
            />
          </n-form-item>

          <n-form-item label="销售限价" path="priceLimit" class="form-item">
            <n-input-number
              v-model:value="formData.priceLimit"
              placeholder="请输入限价金额"
              :precision="0"
              :min="0"
              :step="100"
              style="width: 100%"
              button-placement="both"
              :readonly="isReadonly"
            >
              <template #prefix>￥</template>
            </n-input-number>
          </n-form-item>
        </div>

        <!-- 第二行：生效时间 + 失效时间 -->
        <div class="form-row">
          <n-form-item label="生效时间" path="effectiveTime" class="form-item">
            <n-date-picker
              v-model:value="formData.effectiveTime"
              type="datetime"
              default-time="00:00:00"
              placeholder="请选择生效时间"
              style="width: 100%"
              :disabled="isReadonly"
            />
          </n-form-item>

          <n-form-item label="失效时间" path="expiryTime" class="form-item">
            <n-date-picker
              default-time="23:59:59"
              v-model:value="formData.expiryTime"
              type="datetime"
              placeholder="请选择失效时间"
              style="width: 100%"
              :disabled="isReadonly"
            />
          </n-form-item>
        </div>

        <!-- 第三行：生效范围 + 说明文案 -->
        <div class="form-row">
          <n-form-item label="生效范围" class="form-item">
            <div class="org-selector-container">
              <n-button
                type="primary"
                ghost
                @click="showOrgSelector = true"
                style="width: 100%"
                :disabled="isReadonly"
              >
                <template #icon>
                  <n-icon>
                    <component :is="BuildingIcon" />
                  </n-icon>
                </template>
                {{
                  selectedOrgs.length > 0
                    ? `已选择 ${selectedOrgs.length} 个销售单位`
                    : "指定销售单位（可选）"
                }}
              </n-button>

              <!-- 已选择的机构标签 -->
              <div v-if="selectedOrgs.length > 0" class="selected-orgs">
                <n-tag
                  v-for="org in selectedOrgs"
                  :key="org.id"
                  :closable="!isReadonly"
                  @close="removeOrg(org.id)"
                  style="margin: 4px 4px 0 0"
                >
                  {{ org.orgName }}
                </n-tag>
              </div>
            </div>
          </n-form-item>

          <div class="form-item rule-description">
            <div class="description-title">规则说明：</div>
            <div class="description-item">
              1、该规则将会影响您在下方选择的所有在售车型或车架号
            </div>
            <div class="description-item">
              2、<b style="color: red">车架号</b>的限价优先级将<b
                style="color: red"
                >高于</b
              >在售车型设置的销售限价
            </div>
            <div class="description-item">
              3、限价规则启用后在上述日期区间内（含起止日期）自动生效或失效
            </div>
            <div class="description-item">
              4、未设置生效范围时规则对全集团生效，设置后仅对指定销售单位生效
            </div>
            <div class="description-item">
              5、同一VIN或SKU存在于不同规则中时，限价以<b style="color: red"
                >最近</b
              >启用的规则为准
            </div>
          </div>
        </div>
      </n-card>

      <!-- 规则明细 -->
      <n-card class="form-section">
        <template #header>
          <div class="rule-detail-header">
            <span class="rule-detail-label">规则类型</span>
            <n-radio-group
              v-model:value="formData.ruleType"
              @update:value="handleRuleTypeChange"
              class="rule-type-selector"
              :disabled="isReadonly"
            >
              <n-radio-button value="sku">SKU</n-radio-button>
              <n-radio-button value="vin">VIN</n-radio-button>
            </n-radio-group>
          </div>
        </template>
        <!-- SKU模式的联动下拉框 -->
        <div v-if="formData.ruleType === 'sku'" class="sku-selector">
          <n-space align="end">
            <n-form-item label="品牌">
              <n-select
                v-model:value="skuForm.vehicleBrand"
                placeholder="请选择车辆品牌"
                style="width: 200px"
                :options="vehicleBrandOptions"
                clearable
                :disabled="isReadonly"
                @update:value="handleBrandChange"
              />
            </n-form-item>

            <n-form-item label="车型">
              <n-select
                v-model:value="skuForm.vehicleModel"
                placeholder="请选择车型"
                style="width: 200px"
                :options="vehicleModelOptions"
                :disabled="!skuForm.vehicleBrand || isReadonly"
                :loading="loadingModels"
                clearable
                filterable
                @update:value="handleModelChange"
              />
            </n-form-item>

            <n-form-item label="配置">
              <n-select
                v-model:value="skuForm.vehicleConfig"
                placeholder="请选择配置"
                style="width: 200px"
                :options="vehicleConfigOptions"
                :disabled="!skuForm.vehicleModel || isReadonly"
                :loading="loadingConfigs"
                clearable
                filterable
              />
            </n-form-item>

            <n-form-item label=" ">
              <n-button
                type="primary"
                @click="querySku"
                :disabled="!canQuerySku || isReadonly"
              >
                查询
              </n-button>
            </n-form-item>
          </n-space>
        </div>

        <!-- VIN模式的车辆库存选择器 -->
        <div v-if="formData.ruleType === 'vin'" class="vin-selector">
          <n-button
            type="primary"
            @click="showVehicleSelector"
            :disabled="isReadonly"
          >
            选择车辆
          </n-button>
        </div>

        <!-- 规则明细表格 -->
        <div class="rule-details-table">
          <n-data-table
            :columns="detailColumns"
            :data="ruleDetails"
            :pagination="pagination"
            :loading="detailLoading"
            :row-key="(row) => row.id"
            striped
            flex-height
            style="min-height: 300px"
          />
        </div>
      </n-card>
    </n-form>

    <!-- 底部按钮 -->
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">
          {{ mode === "view" ? "关闭" : "取消" }}
        </n-button>

        <!-- 新增/编辑模式：显示保存按钮 -->
        <n-button
          v-if="mode !== 'view'"
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存
        </n-button>
      </n-space>
    </template>

    <!-- 车辆库存选择器 -->
    <vehicle-stocks-selector
      v-model:visible="vehicleSelectorVisible"
      :multiple="true"
      @confirm="handleVehicleSelected"
      @cancel="handleVehicleSelectorCancel"
    />

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择生效机构"
      business-permission="can_sell"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />
  </n-modal>
</template>

<script setup>
import { useAddSalesPriceLimitRule } from "./AddSalesPriceLimitRule.js";
import VehicleStocksSelector from "@/components/inventory/VehicleStocksSelector.vue";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import { Building } from "@vicons/tabler";

// 使用组合式函数
const {
  // 状态
  visible,
  formRef,
  formData,
  formRules,
  skuForm,
  ruleDetails,
  detailLoading,
  saving,
  vehicleSelectorVisible,
  pagination,
  loadingModels,
  loadingConfigs,

  // 机构选择相关状态
  showOrgSelector,
  selectedOrgs,

  // 选项数据
  vehicleBrandOptions,
  vehicleModelOptions,
  vehicleConfigOptions,
  detailColumns,

  // 计算属性
  canQuerySku,
  modalTitle,
  isReadonly,

  // 模式相关状态
  mode,

  // 方法
  handleCancel,
  handleSave,
  handleBrandChange,
  handleModelChange,
  handleRuleTypeChange,
  querySku,
  showVehicleSelector,
  handleVehicleSelected,
  handleVehicleSelectorCancel,
  show,

  // 机构选择相关方法
  handleOrgSelect,
  handleOrgCancel,
  removeOrg,
} = useAddSalesPriceLimitRule();

// 图标组件
const BuildingIcon = Building;

// 暴露方法给父组件
defineExpose({
  show,
  hide: () => {
    visible.value = false;
  },
});
</script>

<style lang="scss" scoped>
.form-section {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-item {
  flex: 1;
}

.sku-selector {
  margin-bottom: 16px;
}

.vin-selector {
  margin-bottom: 16px;
}

.rule-details-table {
  margin-top: 16px;
}

.rule-description {
  padding-left: 16px;
}

.description-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.description-item {
  color: #666;
  font-size: 13px;
  line-height: 1.6;
  margin-bottom: 4px;
  padding-left: 8px;
  position: relative;
}

.description-item:last-child {
  margin-bottom: 0;
}

.org-selector-container {
  width: 100%;
}

.selected-orgs {
  margin-top: 8px;
  min-height: 20px;
}

.rule-detail-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.rule-detail-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  min-width: 80px;
}

.rule-type-selector {
  flex: 1;
}
</style>
